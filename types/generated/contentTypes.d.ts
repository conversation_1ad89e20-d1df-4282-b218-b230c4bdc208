import type { Attribute, Schema } from '@strapi/strapi';

export interface AdminApiToken extends Schema.CollectionType {
  collectionName: 'strapi_api_tokens';
  info: {
    description: '';
    displayName: 'Api Token';
    name: 'Api Token';
    pluralName: 'api-tokens';
    singularName: 'api-token';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    accessKey: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::api-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    description: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }> &
      Attribute.DefaultTo<''>;
    expiresAt: Attribute.DateTime;
    lastUsedAt: Attribute.DateTime;
    lifespan: Attribute.BigInteger;
    name: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    permissions: Attribute.Relation<
      'admin::api-token',
      'oneToMany',
      'admin::api-token-permission'
    >;
    type: Attribute.Enumeration<['read-only', 'full-access', 'custom']> &
      Attribute.Required &
      Attribute.DefaultTo<'read-only'>;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'admin::api-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminApiTokenPermission extends Schema.CollectionType {
  collectionName: 'strapi_api_token_permissions';
  info: {
    description: '';
    displayName: 'API Token Permission';
    name: 'API Token Permission';
    pluralName: 'api-token-permissions';
    singularName: 'api-token-permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::api-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    token: Attribute.Relation<
      'admin::api-token-permission',
      'manyToOne',
      'admin::api-token'
    >;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'admin::api-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminPermission extends Schema.CollectionType {
  collectionName: 'admin_permissions';
  info: {
    description: '';
    displayName: 'Permission';
    name: 'Permission';
    pluralName: 'permissions';
    singularName: 'permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    actionParameters: Attribute.JSON & Attribute.DefaultTo<{}>;
    conditions: Attribute.JSON & Attribute.DefaultTo<[]>;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    properties: Attribute.JSON & Attribute.DefaultTo<{}>;
    role: Attribute.Relation<'admin::permission', 'manyToOne', 'admin::role'>;
    subject: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'admin::permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminRole extends Schema.CollectionType {
  collectionName: 'admin_roles';
  info: {
    description: '';
    displayName: 'Role';
    name: 'Role';
    pluralName: 'roles';
    singularName: 'role';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    code: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'admin::role', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    description: Attribute.String;
    name: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    permissions: Attribute.Relation<
      'admin::role',
      'oneToMany',
      'admin::permission'
    >;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<'admin::role', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    users: Attribute.Relation<'admin::role', 'manyToMany', 'admin::user'>;
  };
}

export interface AdminTransferToken extends Schema.CollectionType {
  collectionName: 'strapi_transfer_tokens';
  info: {
    description: '';
    displayName: 'Transfer Token';
    name: 'Transfer Token';
    pluralName: 'transfer-tokens';
    singularName: 'transfer-token';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    accessKey: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::transfer-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    description: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }> &
      Attribute.DefaultTo<''>;
    expiresAt: Attribute.DateTime;
    lastUsedAt: Attribute.DateTime;
    lifespan: Attribute.BigInteger;
    name: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    permissions: Attribute.Relation<
      'admin::transfer-token',
      'oneToMany',
      'admin::transfer-token-permission'
    >;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'admin::transfer-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminTransferTokenPermission extends Schema.CollectionType {
  collectionName: 'strapi_transfer_token_permissions';
  info: {
    description: '';
    displayName: 'Transfer Token Permission';
    name: 'Transfer Token Permission';
    pluralName: 'transfer-token-permissions';
    singularName: 'transfer-token-permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::transfer-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    token: Attribute.Relation<
      'admin::transfer-token-permission',
      'manyToOne',
      'admin::transfer-token'
    >;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'admin::transfer-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminUser extends Schema.CollectionType {
  collectionName: 'admin_users';
  info: {
    description: '';
    displayName: 'User';
    name: 'User';
    pluralName: 'users';
    singularName: 'user';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    blocked: Attribute.Boolean & Attribute.Private & Attribute.DefaultTo<false>;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'admin::user', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    email: Attribute.Email &
      Attribute.Required &
      Attribute.Private &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    firstname: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    isActive: Attribute.Boolean &
      Attribute.Private &
      Attribute.DefaultTo<false>;
    lastname: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    password: Attribute.Password &
      Attribute.Private &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    preferedLanguage: Attribute.String;
    registrationToken: Attribute.String & Attribute.Private;
    resetPasswordToken: Attribute.String & Attribute.Private;
    roles: Attribute.Relation<'admin::user', 'manyToMany', 'admin::role'> &
      Attribute.Private;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<'admin::user', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    username: Attribute.String;
  };
}

export interface ApiActivityLevelActivityLevel extends Schema.CollectionType {
  collectionName: 'activity_levels';
  info: {
    displayName: 'Activity Level';
    pluralName: 'activity-levels';
    singularName: 'activity-level';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::activity-level.activity-level',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    level: Attribute.Enumeration<['low', 'medium', 'high']>;
    note: Attribute.Text;
    nursery: Attribute.Relation<
      'api::activity-level.activity-level',
      'oneToOne',
      'api::nursery.nursery'
    >;
    publishedAt: Attribute.DateTime;
    student: Attribute.Relation<
      'api::activity-level.activity-level',
      'oneToOne',
      'api::student.student'
    >;
    teacher: Attribute.Relation<
      'api::activity-level.activity-level',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::activity-level.activity-level',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiActivityActivity extends Schema.CollectionType {
  collectionName: 'activities';
  info: {
    description: '';
    displayName: 'Activity';
    pluralName: 'activities';
    singularName: 'activity';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    children: Attribute.Relation<
      'api::activity.activity',
      'oneToMany',
      'admin::user'
    >;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::activity.activity',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    description: Attribute.String;
    from: Attribute.DateTime;
    image: Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    name: Attribute.String;
    nursery: Attribute.Relation<
      'api::activity.activity',
      'oneToOne',
      'api::nursery.nursery'
    >;
    publishedAt: Attribute.DateTime;
    teacher: Attribute.Relation<
      'api::activity.activity',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    to: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::activity.activity',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiAnnouncementAnnouncement extends Schema.CollectionType {
  collectionName: 'announcements';
  info: {
    displayName: 'Announcement';
    pluralName: 'announcements';
    singularName: 'announcement';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::announcement.announcement',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    description: Attribute.Text;
    nursery: Attribute.Relation<
      'api::announcement.announcement',
      'oneToOne',
      'api::nursery.nursery'
    >;
    publishedAt: Attribute.DateTime;
    target: Attribute.Enumeration<['admins', 'teachers', 'parents', 'all']>;
    title: Attribute.String;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::announcement.announcement',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiAttendanceAttendance extends Schema.CollectionType {
  collectionName: 'attendances';
  info: {
    description: '';
    displayName: 'Attendance';
    pluralName: 'attendances';
    singularName: 'attendance';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    attendance_date: Attribute.String;
    class: Attribute.Relation<
      'api::attendance.attendance',
      'oneToOne',
      'api::class.class'
    >;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::attendance.attendance',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    nursery: Attribute.Relation<
      'api::attendance.attendance',
      'oneToOne',
      'api::nursery.nursery'
    >;
    publishedAt: Attribute.DateTime;
    student: Attribute.Relation<
      'api::attendance.attendance',
      'oneToOne',
      'api::student.student'
    >;
    teacher: Attribute.Relation<
      'api::attendance.attendance',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::attendance.attendance',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiBillBill extends Schema.CollectionType {
  collectionName: 'bills';
  info: {
    description: '';
    displayName: 'Bill';
    pluralName: 'bills';
    singularName: 'bill';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    amount: Attribute.Decimal;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'api::bill.bill', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    date: Attribute.String;
    name: Attribute.String;
    nursery: Attribute.Relation<
      'api::bill.bill',
      'oneToOne',
      'api::nursery.nursery'
    >;
    publishedAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<'api::bill.bill', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface ApiClassClass extends Schema.CollectionType {
  collectionName: 'classes';
  info: {
    description: '';
    displayName: 'Class';
    pluralName: 'classes';
    singularName: 'class';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    class_students: Attribute.Relation<
      'api::class.class',
      'oneToMany',
      'api::student.student'
    >;
    class_teachers: Attribute.Relation<
      'api::class.class',
      'manyToMany',
      'plugin::users-permissions.user'
    >;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::class.class',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    description: Attribute.Text;
    logo: Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    name: Attribute.String;
    nursery: Attribute.Relation<
      'api::class.class',
      'manyToOne',
      'api::nursery.nursery'
    >;
    publishedAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::class.class',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiConfigConfig extends Schema.SingleType {
  collectionName: 'configs';
  info: {
    description: '';
    displayName: 'Config';
    pluralName: 'configs';
    singularName: 'config';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::config.config',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    on_boarding: Attribute.Component<'on-boarding.on-boarding', true>;
    parent_on_boarding: Attribute.Component<'on-boarding.on-boarding', true>;
    publishedAt: Attribute.DateTime;
    show_new_feature: Attribute.Boolean & Attribute.DefaultTo<false>;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::config.config',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiEventEvent extends Schema.CollectionType {
  collectionName: 'events';
  info: {
    description: '';
    displayName: 'Event';
    pluralName: 'events';
    singularName: 'event';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    admin: Attribute.Relation<
      'api::event.event',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::event.event',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    description: Attribute.Text;
    name: Attribute.String;
    nursery: Attribute.Relation<
      'api::event.event',
      'oneToOne',
      'api::nursery.nursery'
    >;
    publishedAt: Attribute.DateTime;
    selected_classes: Attribute.Relation<
      'api::event.event',
      'oneToMany',
      'api::class.class'
    >;
    start_time: Attribute.String;
    teachers: Attribute.Relation<
      'api::event.event',
      'oneToMany',
      'plugin::users-permissions.user'
    >;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::event.event',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiExamExam extends Schema.CollectionType {
  collectionName: 'exams';
  info: {
    description: '';
    displayName: 'Exam';
    pluralName: 'exams';
    singularName: 'exam';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    class: Attribute.Relation<'api::exam.exam', 'oneToOne', 'api::class.class'>;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'api::exam.exam', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    date: Attribute.String;
    nursery: Attribute.Relation<
      'api::exam.exam',
      'oneToOne',
      'api::nursery.nursery'
    >;
    publishedAt: Attribute.DateTime;
    question: Attribute.Text;
    students_result: Attribute.Component<'student-exam.student-exam', true>;
    teacher: Attribute.Relation<
      'api::exam.exam',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<'api::exam.exam', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface ApiFoodMealFoodMeal extends Schema.CollectionType {
  collectionName: 'food_meals';
  info: {
    description: '';
    displayName: 'Food Meals';
    pluralName: 'food-meals';
    singularName: 'food-meal';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::food-meal.food-meal',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    day: Attribute.Enumeration<
      [
        'Friday',
        'Saturday',
        'Sunday',
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday'
      ]
    >;
    meal: Attribute.String;
    nursery: Attribute.Relation<
      'api::food-meal.food-meal',
      'oneToOne',
      'api::nursery.nursery'
    >;
    publishedAt: Attribute.DateTime;
    type: Attribute.Enumeration<['Breakfast', 'Snack', 'Lunch']>;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::food-meal.food-meal',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiFoodFood extends Schema.CollectionType {
  collectionName: 'foods';
  info: {
    description: '';
    displayName: 'Food';
    pluralName: 'foods';
    singularName: 'food';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'api::food.food', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    meal_amount: Attribute.String;
    meal_type: Attribute.String;
    nursery: Attribute.Relation<
      'api::food.food',
      'oneToOne',
      'api::nursery.nursery'
    >;
    publishedAt: Attribute.DateTime;
    student: Attribute.Relation<
      'api::food.food',
      'oneToOne',
      'api::student.student'
    >;
    teacher: Attribute.Relation<
      'api::food.food',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<'api::food.food', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface ApiHistoryHistory extends Schema.CollectionType {
  collectionName: 'histories';
  info: {
    description: '';
    displayName: 'History';
    pluralName: 'histories';
    singularName: 'history';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    activity: Attribute.Relation<
      'api::history.history',
      'oneToOne',
      'api::teacher-activity.teacher-activity'
    >;
    activity_level: Attribute.Relation<
      'api::history.history',
      'oneToOne',
      'api::activity-level.activity-level'
    >;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::history.history',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    date: Attribute.String;
    food: Attribute.Relation<
      'api::history.history',
      'oneToOne',
      'api::food.food'
    >;
    mood: Attribute.Relation<
      'api::history.history',
      'oneToOne',
      'api::mood.mood'
    >;
    nursery: Attribute.Relation<
      'api::history.history',
      'oneToOne',
      'api::nursery.nursery'
    >;
    publishedAt: Attribute.DateTime;
    sleep: Attribute.Relation<
      'api::history.history',
      'oneToOne',
      'api::sleep.sleep'
    >;
    student: Attribute.Relation<
      'api::history.history',
      'oneToOne',
      'api::student.student'
    >;
    supply: Attribute.Relation<
      'api::history.history',
      'oneToOne',
      'api::supply.supply'
    >;
    teacher: Attribute.Relation<
      'api::history.history',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    toilet: Attribute.Relation<
      'api::history.history',
      'oneToOne',
      'api::toilet.toilet'
    >;
    type: Attribute.Enumeration<
      ['activity', 'food', 'sleep', 'toilet', 'supply', 'mood', 'activityLevel']
    >;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::history.history',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiInvoiceInvoice extends Schema.CollectionType {
  collectionName: 'invoices';
  info: {
    description: '';
    displayName: 'Invoice';
    pluralName: 'invoices';
    singularName: 'invoice';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    amount: Attribute.Decimal;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::invoice.invoice',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    date: Attribute.String;
    name: Attribute.String;
    nursery: Attribute.Relation<
      'api::invoice.invoice',
      'oneToOne',
      'api::nursery.nursery'
    >;
    publishedAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::invoice.invoice',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiJobApplicationJobApplication extends Schema.CollectionType {
  collectionName: 'job_applications';
  info: {
    description: '';
    displayName: 'Job Application';
    pluralName: 'job-applications';
    singularName: 'job-application';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    birth_date: Attribute.Date;
    city: Attribute.String;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::job-application.job-application',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    cv: Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    education: Attribute.String;
    expected_salary: Attribute.Decimal;
    image: Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    job_title: Attribute.String;
    name: Attribute.String;
    phone: Attribute.String;
    publishedAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::job-application.job-application',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiLearningLearning extends Schema.CollectionType {
  collectionName: 'learnings';
  info: {
    description: '';
    displayName: 'Learning';
    pluralName: 'learnings';
    singularName: 'learning';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    child: Attribute.Relation<
      'api::learning.learning',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::learning.learning',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    description: Attribute.Text;
    nursery: Attribute.Relation<
      'api::learning.learning',
      'oneToOne',
      'api::nursery.nursery'
    >;
    publishedAt: Attribute.DateTime;
    teacher: Attribute.Relation<
      'api::learning.learning',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::learning.learning',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiMessageCenterMessageCenter extends Schema.CollectionType {
  collectionName: 'message_centers';
  info: {
    description: '';
    displayName: 'Message Center';
    pluralName: 'message-centers';
    singularName: 'message-center';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    admin: Attribute.Relation<
      'api::message-center.message-center',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::message-center.message-center',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    description: Attribute.Text;
    nursery: Attribute.Relation<
      'api::message-center.message-center',
      'oneToOne',
      'api::nursery.nursery'
    >;
    publishedAt: Attribute.DateTime;
    student: Attribute.Relation<
      'api::message-center.message-center',
      'oneToOne',
      'api::student.student'
    >;
    teacher: Attribute.Relation<
      'api::message-center.message-center',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    title: Attribute.String;
    type: Attribute.Enumeration<['teacher', 'admin', 'parent']>;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::message-center.message-center',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiMoodMood extends Schema.CollectionType {
  collectionName: 'moods';
  info: {
    description: '';
    displayName: 'Mood';
    pluralName: 'moods';
    singularName: 'mood';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'api::mood.mood', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    mood: Attribute.Enumeration<
      [
        'angry',
        'calm',
        'excited',
        'happy',
        'sad',
        'sleepy',
        'unwell',
        'worried'
      ]
    >;
    note: Attribute.Text;
    nursery: Attribute.Relation<
      'api::mood.mood',
      'oneToOne',
      'api::nursery.nursery'
    >;
    publishedAt: Attribute.DateTime;
    student: Attribute.Relation<
      'api::mood.mood',
      'oneToOne',
      'api::student.student'
    >;
    teacher: Attribute.Relation<
      'api::mood.mood',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<'api::mood.mood', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface ApiNotificationNotification extends Schema.CollectionType {
  collectionName: 'notifications';
  info: {
    description: '';
    displayName: 'Notification';
    pluralName: 'notifications';
    singularName: 'notification';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    admin: Attribute.Relation<
      'api::notification.notification',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    body: Attribute.Text;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::notification.notification',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    nursery: Attribute.Relation<
      'api::notification.notification',
      'oneToOne',
      'api::nursery.nursery'
    >;
    parent: Attribute.Relation<
      'api::notification.notification',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    publishedAt: Attribute.DateTime;
    teacher: Attribute.Relation<
      'api::notification.notification',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    title: Attribute.String;
    topic: Attribute.String;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::notification.notification',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiNurseryNursery extends Schema.CollectionType {
  collectionName: 'nurseries';
  info: {
    description: '';
    displayName: 'Nursery';
    pluralName: 'nurseries';
    singularName: 'nursery';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    admin: Attribute.Relation<
      'api::nursery.nursery',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    can_contact_teacher: Attribute.Boolean & Attribute.DefaultTo<true>;
    classes: Attribute.Relation<
      'api::nursery.nursery',
      'oneToMany',
      'api::class.class'
    >;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::nursery.nursery',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    end_date: Attribute.Date;
    fees: Attribute.Integer;
    logo: Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    max_students: Attribute.Integer & Attribute.DefaultTo<5>;
    name: Attribute.String;
    payment_methods: Attribute.Component<'payment-methods.payment-methods'>;
    publishedAt: Attribute.DateTime;
    show_nursery_logo_in_parent_app: Attribute.Boolean &
      Attribute.DefaultTo<false>;
    teachers: Attribute.Relation<
      'api::nursery.nursery',
      'oneToMany',
      'plugin::users-permissions.user'
    >;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::nursery.nursery',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiPlanPlan extends Schema.CollectionType {
  collectionName: 'plans';
  info: {
    description: '';
    displayName: 'Plan';
    pluralName: 'plans';
    singularName: 'plan';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    class: Attribute.Relation<'api::plan.plan', 'oneToOne', 'api::class.class'>;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'api::plan.plan', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    date: Attribute.String;
    nursery: Attribute.Relation<
      'api::plan.plan',
      'oneToOne',
      'api::nursery.nursery'
    >;
    publishedAt: Attribute.DateTime;
    sections: Attribute.Component<'on-boarding.on-boarding', true>;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<'api::plan.plan', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface ApiRequirementRequirement extends Schema.CollectionType {
  collectionName: 'requirements';
  info: {
    description: '';
    displayName: 'Requirement';
    pluralName: 'requirements';
    singularName: 'requirement';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::requirement.requirement',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    description: Attribute.Text;
    name: Attribute.String;
    nursery: Attribute.Relation<
      'api::requirement.requirement',
      'oneToOne',
      'api::nursery.nursery'
    >;
    parent: Attribute.Relation<
      'api::requirement.requirement',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    priority: Attribute.Enumeration<['high', 'low']>;
    publishedAt: Attribute.DateTime;
    teacher: Attribute.Relation<
      'api::requirement.requirement',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::requirement.requirement',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiSleepSleep extends Schema.CollectionType {
  collectionName: 'sleeps';
  info: {
    description: '';
    displayName: 'Sleep';
    pluralName: 'sleeps';
    singularName: 'sleep';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::sleep.sleep',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    from: Attribute.String;
    nursery: Attribute.Relation<
      'api::sleep.sleep',
      'oneToOne',
      'api::nursery.nursery'
    >;
    publishedAt: Attribute.DateTime;
    student: Attribute.Relation<
      'api::sleep.sleep',
      'oneToOne',
      'api::student.student'
    >;
    teacher: Attribute.Relation<
      'api::sleep.sleep',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    to: Attribute.String;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::sleep.sleep',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiStudentStudent extends Schema.CollectionType {
  collectionName: 'students';
  info: {
    description: '';
    displayName: 'Student';
    pluralName: 'students';
    singularName: 'student';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    birth_date: Attribute.String;
    class: Attribute.Relation<
      'api::student.student',
      'manyToOne',
      'api::class.class'
    >;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::student.student',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    fees: Attribute.Integer;
    home_address: Attribute.Text;
    image: Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    is_active: Attribute.Boolean;
    mother_phone_number: Attribute.String;
    name: Attribute.String;
    nursery: Attribute.Relation<
      'api::student.student',
      'oneToOne',
      'api::nursery.nursery'
    >;
    parent_phone_number: Attribute.String;
    pickup_persons: Attribute.Component<
      'pick-up-persons.pick-up-persons',
      true
    >;
    publishedAt: Attribute.DateTime;
    student_gender: Attribute.String & Attribute.DefaultTo<'male'>;
    subscription_date: Attribute.String;
    subscriptions: Attribute.Component<'subscription.subscription', true>;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::student.student',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiSupplySupply extends Schema.CollectionType {
  collectionName: 'supplies';
  info: {
    description: '';
    displayName: 'Supply';
    pluralName: 'supplies';
    singularName: 'supply';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::supply.supply',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    name: Attribute.String;
    nursery: Attribute.Relation<
      'api::supply.supply',
      'oneToOne',
      'api::nursery.nursery'
    >;
    publishedAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::supply.supply',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiTeacherActivityTeacherActivity
  extends Schema.CollectionType {
  collectionName: 'teacher_activities';
  info: {
    description: '';
    displayName: 'Teacher Activity';
    pluralName: 'teacher-activities';
    singularName: 'teacher-activity';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    activity: Attribute.Relation<
      'api::teacher-activity.teacher-activity',
      'oneToOne',
      'api::activity.activity'
    >;
    activity_notes: Attribute.Component<'activity-notes.activity-note', true>;
    class: Attribute.Relation<
      'api::teacher-activity.teacher-activity',
      'oneToOne',
      'api::class.class'
    >;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::teacher-activity.teacher-activity',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    date: Attribute.String;
    day: Attribute.String;
    from: Attribute.String;
    is_weekly_activity: Attribute.Boolean & Attribute.DefaultTo<true>;
    nursery: Attribute.Relation<
      'api::teacher-activity.teacher-activity',
      'oneToOne',
      'api::nursery.nursery'
    >;
    publishedAt: Attribute.DateTime;
    teacher: Attribute.Relation<
      'api::teacher-activity.teacher-activity',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    to: Attribute.String;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::teacher-activity.teacher-activity',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiTeacherSupplyTeacherSupply extends Schema.CollectionType {
  collectionName: 'teacher_supplies';
  info: {
    description: '';
    displayName: 'Teacher Supply';
    pluralName: 'teacher-supplies';
    singularName: 'teacher-supply';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::teacher-supply.teacher-supply',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    mark_as_sent: Attribute.Boolean & Attribute.DefaultTo<false>;
    nursery: Attribute.Relation<
      'api::teacher-supply.teacher-supply',
      'oneToOne',
      'api::nursery.nursery'
    >;
    publishedAt: Attribute.DateTime;
    student: Attribute.Relation<
      'api::teacher-supply.teacher-supply',
      'oneToOne',
      'api::student.student'
    >;
    supplies: Attribute.Relation<
      'api::teacher-supply.teacher-supply',
      'oneToMany',
      'api::supply.supply'
    >;
    teacher: Attribute.Relation<
      'api::teacher-supply.teacher-supply',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::teacher-supply.teacher-supply',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiToiletToilet extends Schema.CollectionType {
  collectionName: 'toilets';
  info: {
    displayName: 'Toilet';
    pluralName: 'toilets';
    singularName: 'toilet';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::toilet.toilet',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    nursery: Attribute.Relation<
      'api::toilet.toilet',
      'oneToOne',
      'api::nursery.nursery'
    >;
    publishedAt: Attribute.DateTime;
    student: Attribute.Relation<
      'api::toilet.toilet',
      'oneToOne',
      'api::student.student'
    >;
    teacher: Attribute.Relation<
      'api::toilet.toilet',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    toilet_type: Attribute.String;
    toilet_way: Attribute.String;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'api::toilet.toilet',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginContentReleasesRelease extends Schema.CollectionType {
  collectionName: 'strapi_releases';
  info: {
    displayName: 'Release';
    pluralName: 'releases';
    singularName: 'release';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    actions: Attribute.Relation<
      'plugin::content-releases.release',
      'oneToMany',
      'plugin::content-releases.release-action'
    >;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::content-releases.release',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    name: Attribute.String & Attribute.Required;
    releasedAt: Attribute.DateTime;
    scheduledAt: Attribute.DateTime;
    status: Attribute.Enumeration<
      ['ready', 'blocked', 'failed', 'done', 'empty']
    > &
      Attribute.Required;
    timezone: Attribute.String;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'plugin::content-releases.release',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginContentReleasesReleaseAction
  extends Schema.CollectionType {
  collectionName: 'strapi_release_actions';
  info: {
    displayName: 'Release Action';
    pluralName: 'release-actions';
    singularName: 'release-action';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    contentType: Attribute.String & Attribute.Required;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::content-releases.release-action',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    entry: Attribute.Relation<
      'plugin::content-releases.release-action',
      'morphToOne'
    >;
    isEntryValid: Attribute.Boolean;
    locale: Attribute.String;
    release: Attribute.Relation<
      'plugin::content-releases.release-action',
      'manyToOne',
      'plugin::content-releases.release'
    >;
    type: Attribute.Enumeration<['publish', 'unpublish']> & Attribute.Required;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'plugin::content-releases.release-action',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginI18NLocale extends Schema.CollectionType {
  collectionName: 'i18n_locale';
  info: {
    collectionName: 'locales';
    description: '';
    displayName: 'Locale';
    pluralName: 'locales';
    singularName: 'locale';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    code: Attribute.String & Attribute.Unique;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::i18n.locale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    name: Attribute.String &
      Attribute.SetMinMax<
        {
          max: 50;
          min: 1;
        },
        number
      >;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'plugin::i18n.locale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUploadFile extends Schema.CollectionType {
  collectionName: 'files';
  info: {
    description: '';
    displayName: 'File';
    pluralName: 'files';
    singularName: 'file';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    alternativeText: Attribute.String;
    caption: Attribute.String;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::upload.file',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    ext: Attribute.String;
    folder: Attribute.Relation<
      'plugin::upload.file',
      'manyToOne',
      'plugin::upload.folder'
    > &
      Attribute.Private;
    folderPath: Attribute.String &
      Attribute.Required &
      Attribute.Private &
      Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      >;
    formats: Attribute.JSON;
    hash: Attribute.String & Attribute.Required;
    height: Attribute.Integer;
    mime: Attribute.String & Attribute.Required;
    name: Attribute.String & Attribute.Required;
    previewUrl: Attribute.String;
    provider: Attribute.String & Attribute.Required;
    provider_metadata: Attribute.JSON;
    related: Attribute.Relation<'plugin::upload.file', 'morphToMany'>;
    size: Attribute.Decimal & Attribute.Required;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'plugin::upload.file',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    url: Attribute.String & Attribute.Required;
    width: Attribute.Integer;
  };
}

export interface PluginUploadFolder extends Schema.CollectionType {
  collectionName: 'upload_folders';
  info: {
    displayName: 'Folder';
    pluralName: 'folders';
    singularName: 'folder';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    children: Attribute.Relation<
      'plugin::upload.folder',
      'oneToMany',
      'plugin::upload.folder'
    >;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::upload.folder',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    files: Attribute.Relation<
      'plugin::upload.folder',
      'oneToMany',
      'plugin::upload.file'
    >;
    name: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      >;
    parent: Attribute.Relation<
      'plugin::upload.folder',
      'manyToOne',
      'plugin::upload.folder'
    >;
    path: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      >;
    pathId: Attribute.Integer & Attribute.Required & Attribute.Unique;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'plugin::upload.folder',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUsersPermissionsPermission
  extends Schema.CollectionType {
  collectionName: 'up_permissions';
  info: {
    description: '';
    displayName: 'Permission';
    name: 'permission';
    pluralName: 'permissions';
    singularName: 'permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String & Attribute.Required;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::users-permissions.permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    role: Attribute.Relation<
      'plugin::users-permissions.permission',
      'manyToOne',
      'plugin::users-permissions.role'
    >;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'plugin::users-permissions.permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUsersPermissionsRole extends Schema.CollectionType {
  collectionName: 'up_roles';
  info: {
    description: '';
    displayName: 'Role';
    name: 'role';
    pluralName: 'roles';
    singularName: 'role';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    description: Attribute.String;
    name: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 3;
      }>;
    permissions: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToMany',
      'plugin::users-permissions.permission'
    >;
    type: Attribute.String & Attribute.Unique;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    users: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToMany',
      'plugin::users-permissions.user'
    >;
  };
}

export interface PluginUsersPermissionsUser extends Schema.CollectionType {
  collectionName: 'up_users';
  info: {
    description: '';
    displayName: 'User';
    name: 'user';
    pluralName: 'users';
    singularName: 'user';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    access_permissions: Attribute.Component<
      'access-permissions.access-permissions',
      true
    >;
    blocked: Attribute.Boolean & Attribute.DefaultTo<false>;
    class_id: Attribute.Integer;
    confirmationToken: Attribute.String & Attribute.Private;
    confirmed: Attribute.Boolean & Attribute.DefaultTo<false>;
    createdAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::users-permissions.user',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    email: Attribute.Email &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    fcm_token: Attribute.String;
    image: Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    is_active: Attribute.Boolean;
    job_title: Attribute.String;
    nursery: Attribute.Relation<
      'plugin::users-permissions.user',
      'oneToOne',
      'api::nursery.nursery'
    >;
    nursery_id: Attribute.Integer;
    password: Attribute.Password &
      Attribute.Private &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    phone: Attribute.String;
    provider: Attribute.String;
    resetPasswordToken: Attribute.String & Attribute.Private;
    role: Attribute.Relation<
      'plugin::users-permissions.user',
      'manyToOne',
      'plugin::users-permissions.role'
    >;
    student: Attribute.Relation<
      'plugin::users-permissions.user',
      'oneToOne',
      'api::student.student'
    >;
    student_id: Attribute.Integer;
    student_ids: Attribute.Component<'student-ids.student-ids', true>;
    teacher_classes: Attribute.Relation<
      'plugin::users-permissions.user',
      'manyToMany',
      'api::class.class'
    >;
    type: Attribute.Enumeration<
      ['admin', 'teacher', 'parent', 'accountant', 'supervisor']
    >;
    updatedAt: Attribute.DateTime;
    updatedBy: Attribute.Relation<
      'plugin::users-permissions.user',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    username: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 3;
      }>;
  };
}

declare module '@strapi/types' {
  export module Shared {
    export interface ContentTypes {
      'admin::api-token': AdminApiToken;
      'admin::api-token-permission': AdminApiTokenPermission;
      'admin::permission': AdminPermission;
      'admin::role': AdminRole;
      'admin::transfer-token': AdminTransferToken;
      'admin::transfer-token-permission': AdminTransferTokenPermission;
      'admin::user': AdminUser;
      'api::activity-level.activity-level': ApiActivityLevelActivityLevel;
      'api::activity.activity': ApiActivityActivity;
      'api::announcement.announcement': ApiAnnouncementAnnouncement;
      'api::attendance.attendance': ApiAttendanceAttendance;
      'api::bill.bill': ApiBillBill;
      'api::class.class': ApiClassClass;
      'api::config.config': ApiConfigConfig;
      'api::event.event': ApiEventEvent;
      'api::exam.exam': ApiExamExam;
      'api::food-meal.food-meal': ApiFoodMealFoodMeal;
      'api::food.food': ApiFoodFood;
      'api::history.history': ApiHistoryHistory;
      'api::invoice.invoice': ApiInvoiceInvoice;
      'api::job-application.job-application': ApiJobApplicationJobApplication;
      'api::learning.learning': ApiLearningLearning;
      'api::message-center.message-center': ApiMessageCenterMessageCenter;
      'api::mood.mood': ApiMoodMood;
      'api::notification.notification': ApiNotificationNotification;
      'api::nursery.nursery': ApiNurseryNursery;
      'api::plan.plan': ApiPlanPlan;
      'api::requirement.requirement': ApiRequirementRequirement;
      'api::sleep.sleep': ApiSleepSleep;
      'api::student.student': ApiStudentStudent;
      'api::supply.supply': ApiSupplySupply;
      'api::teacher-activity.teacher-activity': ApiTeacherActivityTeacherActivity;
      'api::teacher-supply.teacher-supply': ApiTeacherSupplyTeacherSupply;
      'api::toilet.toilet': ApiToiletToilet;
      'plugin::content-releases.release': PluginContentReleasesRelease;
      'plugin::content-releases.release-action': PluginContentReleasesReleaseAction;
      'plugin::i18n.locale': PluginI18NLocale;
      'plugin::upload.file': PluginUploadFile;
      'plugin::upload.folder': PluginUploadFolder;
      'plugin::users-permissions.permission': PluginUsersPermissionsPermission;
      'plugin::users-permissions.role': PluginUsersPermissionsRole;
      'plugin::users-permissions.user': PluginUsersPermissionsUser;
    }
  }
}
