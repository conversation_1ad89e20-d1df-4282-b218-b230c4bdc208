{"kind": "collectionType", "collectionName": "up_users", "info": {"name": "user", "description": "", "singularName": "user", "pluralName": "users", "displayName": "User"}, "options": {"draftAndPublish": false}, "attributes": {"username": {"type": "string", "minLength": 3, "unique": true, "configurable": false, "required": true}, "email": {"type": "email", "minLength": 6, "configurable": false, "required": true}, "provider": {"type": "string", "configurable": false}, "password": {"type": "password", "minLength": 6, "configurable": false, "private": true, "searchable": false}, "resetPasswordToken": {"type": "string", "configurable": false, "private": true, "searchable": false}, "confirmationToken": {"type": "string", "configurable": false, "private": true, "searchable": false}, "confirmed": {"type": "boolean", "default": false, "configurable": false}, "blocked": {"type": "boolean", "default": false, "configurable": false}, "role": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.role", "inversedBy": "users", "configurable": false}, "phone": {"type": "string"}, "type": {"type": "enumeration", "enum": ["admin", "teacher", "parent", "accountant", "supervisor"]}, "job_title": {"type": "string"}, "image": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "nursery": {"type": "relation", "relation": "oneToOne", "target": "api::nursery.nursery"}, "is_active": {"type": "boolean"}, "fcm_token": {"type": "string"}, "student": {"type": "relation", "relation": "oneToOne", "target": "api::student.student"}, "student_id": {"type": "integer"}, "class_id": {"type": "integer"}, "nursery_id": {"type": "integer"}, "teacher_classes": {"type": "relation", "relation": "manyToMany", "target": "api::class.class", "inversedBy": "class_teachers"}, "student_ids": {"displayName": "Student Ids", "type": "component", "repeatable": true, "component": "student-ids.student-ids"}, "access_permissions": {"displayName": "Access Permissions", "type": "component", "repeatable": true, "component": "access-permissions.access-permissions"}}}