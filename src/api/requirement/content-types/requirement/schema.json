{"kind": "collectionType", "collectionName": "requirements", "info": {"singularName": "requirement", "pluralName": "requirements", "displayName": "Requirement", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "description": {"type": "text"}, "priority": {"type": "enumeration", "enum": ["high", "low"]}, "parent": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "teacher": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "nursery": {"type": "relation", "relation": "oneToOne", "target": "api::nursery.nursery"}}}