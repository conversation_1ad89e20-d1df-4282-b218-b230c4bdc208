{"kind": "collectionType", "collectionName": "histories", "info": {"singularName": "history", "pluralName": "histories", "displayName": "History", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"type": {"type": "enumeration", "enum": ["activity", "food", "sleep", "toilet", "supply", "mood", "activityLevel"]}, "nursery": {"type": "relation", "relation": "oneToOne", "target": "api::nursery.nursery"}, "activity": {"type": "relation", "relation": "oneToOne", "target": "api::teacher-activity.teacher-activity"}, "food": {"type": "relation", "relation": "oneToOne", "target": "api::food.food"}, "sleep": {"type": "relation", "relation": "oneToOne", "target": "api::sleep.sleep"}, "supply": {"type": "relation", "relation": "oneToOne", "target": "api::supply.supply"}, "teacher": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "student": {"type": "relation", "relation": "oneToOne", "target": "api::student.student"}, "date": {"type": "string"}, "toilet": {"type": "relation", "relation": "oneToOne", "target": "api::toilet.toilet"}, "mood": {"type": "relation", "relation": "oneToOne", "target": "api::mood.mood"}, "activity_level": {"type": "relation", "relation": "oneToOne", "target": "api::activity-level.activity-level"}}}