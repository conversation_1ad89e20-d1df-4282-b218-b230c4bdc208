{"kind": "collectionType", "collectionName": "events", "info": {"singularName": "event", "pluralName": "events", "displayName": "Event", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"description": {"type": "text"}, "admin": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "nursery": {"type": "relation", "relation": "oneToOne", "target": "api::nursery.nursery"}, "teachers": {"type": "relation", "relation": "oneToMany", "target": "plugin::users-permissions.user"}, "name": {"type": "string"}, "start_time": {"type": "string"}, "selected_classes": {"type": "relation", "relation": "oneToMany", "target": "api::class.class"}}}