{"kind": "collectionType", "collectionName": "teacher_supplies", "info": {"singularName": "teacher-supply", "pluralName": "teacher-supplies", "displayName": "Teacher Supply", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"supplies": {"type": "relation", "relation": "oneToMany", "target": "api::supply.supply"}, "student": {"type": "relation", "relation": "oneToOne", "target": "api::student.student"}, "nursery": {"type": "relation", "relation": "oneToOne", "target": "api::nursery.nursery"}, "teacher": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "mark_as_sent": {"type": "boolean", "default": false}}}