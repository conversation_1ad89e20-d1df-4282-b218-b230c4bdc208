{"kind": "collectionType", "collectionName": "activities", "info": {"singularName": "activity", "pluralName": "activities", "displayName": "Activity", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "description": {"type": "string"}, "image": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "from": {"type": "datetime"}, "to": {"type": "datetime"}, "teacher": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "children": {"type": "relation", "relation": "oneToMany", "target": "admin::user"}, "nursery": {"type": "relation", "relation": "oneToOne", "target": "api::nursery.nursery"}}}