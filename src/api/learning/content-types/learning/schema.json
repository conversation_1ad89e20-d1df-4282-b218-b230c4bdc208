{"kind": "collectionType", "collectionName": "learnings", "info": {"singularName": "learning", "pluralName": "learnings", "displayName": "Learning", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"description": {"type": "text"}, "teacher": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "child": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "nursery": {"type": "relation", "relation": "oneToOne", "target": "api::nursery.nursery"}}}