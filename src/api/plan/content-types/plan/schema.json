{"kind": "collectionType", "collectionName": "plans", "info": {"singularName": "plan", "pluralName": "plans", "displayName": "Plan", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"sections": {"type": "component", "repeatable": true, "component": "on-boarding.on-boarding"}, "class": {"type": "relation", "relation": "oneToOne", "target": "api::class.class"}, "nursery": {"type": "relation", "relation": "oneToOne", "target": "api::nursery.nursery"}, "date": {"type": "string"}}}