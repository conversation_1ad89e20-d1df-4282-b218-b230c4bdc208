{"kind": "collectionType", "collectionName": "classes", "info": {"singularName": "class", "pluralName": "classes", "displayName": "Class", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "logo": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "nursery": {"type": "relation", "relation": "manyToOne", "target": "api::nursery.nursery", "inversedBy": "classes"}, "description": {"type": "text"}, "class_students": {"type": "relation", "relation": "oneToMany", "target": "api::student.student", "mappedBy": "class"}, "class_teachers": {"type": "relation", "relation": "manyToMany", "target": "plugin::users-permissions.user", "mappedBy": "teacher_classes"}}}