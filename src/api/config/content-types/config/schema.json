{"kind": "singleType", "collectionName": "configs", "info": {"singularName": "config", "pluralName": "configs", "displayName": "Config", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"on_boarding": {"displayName": "On Boarding", "type": "component", "repeatable": true, "component": "on-boarding.on-boarding"}, "parent_on_boarding": {"type": "component", "repeatable": true, "component": "on-boarding.on-boarding"}, "show_new_feature": {"type": "boolean", "default": false}}}