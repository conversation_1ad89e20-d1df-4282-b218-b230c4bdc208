{"kind": "collectionType", "collectionName": "invoices", "info": {"singularName": "invoice", "pluralName": "invoices", "displayName": "Invoice", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "date": {"type": "string"}, "amount": {"type": "decimal"}, "nursery": {"type": "relation", "relation": "oneToOne", "target": "api::nursery.nursery"}}}