'use strict';

/**
 * mood controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::mood.mood', ({ strapi }) => ({
  async getMoodByDay(ctx) {
    try {
      const { query } = ctx;

      // Extract filters
      const filters = {};

      // Handle student filter
      if (query.filters?.student?.id) {
        filters.student = { id: query.filters.student.id };
      }

      // Handle nursery filter
      if (query.filters?.nursery?.id) {
        filters.nursery = { id: query.filters.nursery.id };
      }

      // Handle teacher filter
      if (query.filters?.teacher?.id) {
        filters.teacher = { id: query.filters.teacher.id };
      }

      // Handle date filtering (createdAt date range)
      if (query.filters?.createdAt) {
        filters.createdAt = query.filters.createdAt;
      }

      // Hardcoded populate parameters
      const populate = {
        student: {
          populate: {
            image: true
          }
        },
        teacher: {
          populate: {
            logo: true
          }
        },
        nursery: {
          populate: {
            logo: true
          }
        }
      };

      // Handle sorting
      const sort = query.sort || 'createdAt:desc';

      // Build query
      const moodQuery = {
        filters,
        populate,
        sort,
      };

      // Add pagination if provided
      if (query.pagination) {
        moodQuery.pagination = query.pagination;
      }

      // Fetch mood data
      const moods = await strapi.entityService.findMany('api::mood.mood', moodQuery);

      // Return data
      ctx.send({
        data: moods,
        meta: {
          count: moods.length,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      ctx.throw(500, `Failed to fetch mood data: ${error.message}`);
    }
  },
}));
