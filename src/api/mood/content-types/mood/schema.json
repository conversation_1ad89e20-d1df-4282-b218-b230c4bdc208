{"kind": "collectionType", "collectionName": "moods", "info": {"singularName": "mood", "pluralName": "moods", "displayName": "<PERSON><PERSON>", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"mood": {"type": "enumeration", "enum": ["angry", "calm", "excited", "happy", "sad", "sleepy", "unwell", "worried"]}, "note": {"type": "text"}, "student": {"type": "relation", "relation": "oneToOne", "target": "api::student.student"}, "nursery": {"type": "relation", "relation": "oneToOne", "target": "api::nursery.nursery"}, "teacher": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}}}