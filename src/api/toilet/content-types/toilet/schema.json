{"kind": "collectionType", "collectionName": "toilets", "info": {"singularName": "toilet", "pluralName": "toilets", "displayName": "<PERSON><PERSON><PERSON>"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"toilet_type": {"type": "string"}, "toilet_way": {"type": "string"}, "student": {"type": "relation", "relation": "oneToOne", "target": "api::student.student"}, "teacher": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "nursery": {"type": "relation", "relation": "oneToOne", "target": "api::nursery.nursery"}}}