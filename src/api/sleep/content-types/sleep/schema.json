{"kind": "collectionType", "collectionName": "sleeps", "info": {"singularName": "sleep", "pluralName": "sleeps", "displayName": "Sleep", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"teacher": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "student": {"type": "relation", "relation": "oneToOne", "target": "api::student.student"}, "nursery": {"type": "relation", "relation": "oneToOne", "target": "api::nursery.nursery"}, "from": {"type": "string"}, "to": {"type": "string"}}}