{"kind": "collectionType", "collectionName": "announcements", "info": {"singularName": "announcement", "pluralName": "announcements", "displayName": "Announcement"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "description": {"type": "text"}, "target": {"type": "enumeration", "enum": ["admins", "teachers", "parents", "all"]}, "nursery": {"type": "relation", "relation": "oneToOne", "target": "api::nursery.nursery"}}}