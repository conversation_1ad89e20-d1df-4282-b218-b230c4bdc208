{"kind": "collectionType", "collectionName": "message_centers", "info": {"singularName": "message-center", "pluralName": "message-centers", "displayName": "Message Center", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "description": {"type": "text"}, "admin": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "nursery": {"type": "relation", "relation": "oneToOne", "target": "api::nursery.nursery"}, "teacher": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "student": {"type": "relation", "relation": "oneToOne", "target": "api::student.student"}, "type": {"type": "enumeration", "enum": ["teacher", "admin", "parent"]}}}