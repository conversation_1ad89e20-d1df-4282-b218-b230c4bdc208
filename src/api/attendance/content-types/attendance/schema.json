{"kind": "collectionType", "collectionName": "attendances", "info": {"singularName": "attendance", "pluralName": "attendances", "displayName": "Attendance", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"teacher": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "nursery": {"type": "relation", "relation": "oneToOne", "target": "api::nursery.nursery"}, "student": {"type": "relation", "relation": "oneToOne", "target": "api::student.student"}, "attendance_date": {"type": "string"}, "class": {"type": "relation", "relation": "oneToOne", "target": "api::class.class"}}}