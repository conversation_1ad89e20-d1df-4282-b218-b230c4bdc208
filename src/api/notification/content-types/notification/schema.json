{"kind": "collectionType", "collectionName": "notifications", "info": {"singularName": "notification", "pluralName": "notifications", "displayName": "Notification", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "body": {"type": "text"}, "admin": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "teacher": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "parent": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "nursery": {"type": "relation", "relation": "oneToOne", "target": "api::nursery.nursery"}, "topic": {"type": "string"}}}