{"kind": "collectionType", "collectionName": "students", "info": {"singularName": "student", "pluralName": "students", "displayName": "Student", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "image": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "nursery": {"type": "relation", "relation": "oneToOne", "target": "api::nursery.nursery"}, "home_address": {"type": "text"}, "mother_phone_number": {"type": "string"}, "parent_phone_number": {"type": "string"}, "class": {"type": "relation", "relation": "manyToOne", "target": "api::class.class", "inversedBy": "class_students"}, "birth_date": {"type": "string"}, "is_active": {"type": "boolean"}, "student_gender": {"type": "string", "default": "male"}, "fees": {"type": "integer"}, "subscriptions": {"displayName": "Subscription", "type": "component", "repeatable": true, "component": "subscription.subscription"}, "pickup_persons": {"displayName": "Pick Up Persons", "type": "component", "repeatable": true, "component": "pick-up-persons.pick-up-persons"}, "subscription_date": {"type": "string"}}}