'use strict';

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::student.student', ({ strapi }) => ({
  // Function to get the count of active students
  async activeStudentsCount(ctx) {
    const { query } = ctx;

    // Query to count students with `is_active: true`
    const activeStudents = await strapi.entityService.count('api::student.student', {
      ...query,
      filters: {
        ...query.filters,
        is_active: true,
      },
    });

    // Send the count as a response
    ctx.send({ count: activeStudents });
  },

  // Function to handle subscription statistics
  async subscriptions(ctx) {
    const { query } = ctx;

    // Get the current year and month
    const currentMonth = new Date().getMonth() + 1;
    const currentYear = new Date().getFullYear();
    const startDate = new Date(currentYear, currentMonth - 1, 1).toISOString().split('T')[0];
    const endDate = new Date(currentYear, currentMonth, 0).toISOString().split('T')[0];

    // Fetch all students with their subscriptions
    const students = await strapi.db.query('api::student.student').findMany({
      where: query.filters || {}, // Apply any additional filters from the query
      populate: ['subscriptions'], // Populate subscriptions to process them in JavaScript
    });

    // Initialize counters
    let totalPaidStudents = 0;
    let totalUnpaidStudents = 0;
    let totalPaidAmounts = 0;

    // Process each student to calculate statistics
    students.forEach((student) => {
      // Filter subscriptions for the current month
      const subscriptionsForMonth = student.subscriptions.filter((sub) => {
        const subDate = new Date(sub.date).toISOString().split('T')[0];
        return subDate >= startDate && subDate <= endDate;
      });

      // Determine if the student has paid subscriptions
      const hasPaid = subscriptionsForMonth.some((sub) => sub.is_paid);

      if (hasPaid) {
        totalPaidStudents++;
        totalPaidAmounts += subscriptionsForMonth.reduce((sum, sub) => {
          return sub.is_paid ? sum + (sub.amount || 0) : sum;
        }, 0);
      } else {
        totalUnpaidStudents++;
      }
    });

    // Calculate total students
    const totalStudents = students.length;

    // Send the calculated statistics
    ctx.send({
      totalStudents,
      totalPaidStudents,
      totalUnpaidStudents,
      totalPaidAmounts,
    });
  },
}));
