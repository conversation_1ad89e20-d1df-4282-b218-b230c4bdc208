{"kind": "collectionType", "collectionName": "bills", "info": {"singularName": "bill", "pluralName": "bills", "displayName": "Bill", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "date": {"type": "string"}, "amount": {"type": "decimal"}, "nursery": {"type": "relation", "relation": "oneToOne", "target": "api::nursery.nursery"}}}