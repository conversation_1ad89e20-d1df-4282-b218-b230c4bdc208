{"kind": "collectionType", "collectionName": "nurseries", "info": {"singularName": "nursery", "pluralName": "nurseries", "displayName": "Nursery", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "logo": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "admin": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "teachers": {"type": "relation", "relation": "oneToMany", "target": "plugin::users-permissions.user"}, "classes": {"type": "relation", "relation": "oneToMany", "target": "api::class.class", "mappedBy": "nursery"}, "max_students": {"type": "integer", "default": 5}, "can_contact_teacher": {"type": "boolean", "default": true}, "show_nursery_logo_in_parent_app": {"type": "boolean", "default": false}, "fees": {"type": "integer"}, "end_date": {"type": "date"}, "payment_methods": {"displayName": "Payment Methods", "type": "component", "repeatable": false, "component": "payment-methods.payment-methods"}}}