'use strict';

/**
 * teacher-activity controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::teacher-activity.teacher-activity', ({ strapi }) => ({
  async parentActivities(ctx) {
    try {
      const { query } = ctx;

      // Extract common filters that apply to all APIs
      const commonFilters = {};
      const teacherActivityFilters = {};

      // Handle student filter
      if (query.filters?.student?.id) {
        commonFilters.student = { id: query.filters.student.id };
      }

      // Handle nursery filter
      if (query.filters?.nursery?.id) {
        commonFilters.nursery = { id: query.filters.nursery.id };
      }

      // Handle class filter (only for teacher-activity)
      if (query.filters?.class?.id) {
        teacherActivityFilters.class = { id: query.filters.class.id };
      }

      // Handle date filtering
      let dateFilters = {};
      let teacherActivityDateFilters = {};

      // For createdAt date range filtering (food, toilet, sleep, mood, activity-level)
      if (query.filters?.createdAt) {
        dateFilters.createdAt = query.filters.createdAt;
      }

      // For teacher-activity day/date filtering
      if (query.filters?.$or) {
        teacherActivityDateFilters.$or = query.filters.$or;
      }

      // Hardcoded populate parameters for teacher-activity
      const teacherActivityPopulate = {
        teacher: {
          populate: {
            logo: true
          }
        },
        activity_notes: {
          populate: {
            media: true
          }
        },
        activity: {
          populate: {
            image: true
          }
        },
        class: {
          populate: {
            logo: true
          }
        }
      };

      // Handle sorting
      const sort = query.sort || 'createdAt:desc';

      // Combine filters for each API
      const teacherActivityQuery = {
        filters: { ...commonFilters, ...teacherActivityFilters, ...teacherActivityDateFilters },
        populate: teacherActivityPopulate,
        sort,
      };

      const commonQuery = {
        filters: { ...commonFilters, ...dateFilters },
        sort,
      };

      // Helper function to transform entity data to REST API format
      const transformToRestFormat = (entities) => {
        if (!entities || !Array.isArray(entities)) return [];

        return entities.map(entity => {
          const { id, ...attributes } = entity;
          return {
            id,
            attributes
          };
        });
      };

      // Fetch data from all APIs in parallel
      const [
        teacherActivitiesRaw,
        foodsRaw,
        toiletsRaw,
        sleepsRaw,
        moodsRaw,
        activityLevelsRaw
      ] = await Promise.all([
        strapi.entityService.findMany('api::teacher-activity.teacher-activity', teacherActivityQuery),
        strapi.entityService.findMany('api::food.food', commonQuery),
        strapi.entityService.findMany('api::toilet.toilet', commonQuery),
        strapi.entityService.findMany('api::sleep.sleep', commonQuery),
        strapi.entityService.findMany('api::mood.mood', commonQuery),
        strapi.entityService.findMany('api::activity-level.activity-level', commonQuery),
      ]);

      // Transform to REST API format
      let teacherActivities = transformToRestFormat(teacherActivitiesRaw);
      const foods = transformToRestFormat(foodsRaw);
      const toilets = transformToRestFormat(toiletsRaw);
      const sleeps = transformToRestFormat(sleepsRaw);
      const moods = transformToRestFormat(moodsRaw);
      const activityLevels = transformToRestFormat(activityLevelsRaw);

      // Filter teacher activities based on date and is_weekly_activity
      // If activity is not weekly (is_weekly_activity = false) and has a date,
      // only show it if the date matches today's date
      const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format

      teacherActivities = teacherActivities.filter(activity => {
        // If activity has a date and is not weekly
        if (activity.attributes.date && !activity.attributes.is_weekly_activity) {
          // Only include if date matches today
          return activity.attributes.date === today;
        }
        // Include all weekly activities or those without dates
        return true;
      })

      // Return combined data in REST API format
      ctx.send({
        data: {
          teacherActivities: { data: teacherActivities },
          foods: { data: foods },
          toilets: { data: toilets },
          sleeps: { data: sleeps },
          moods: { data: moods },
          activityLevels: { data: activityLevels }
        },
        meta: {
          counts: {
            teacherActivities: teacherActivities.length,
            foods: foods.length,
            toilets: toilets.length,
            sleeps: sleeps.length,
            moods: moods.length,
            activityLevels: activityLevels.length,
            total: teacherActivities.length + foods.length + toilets.length + sleeps.length + moods.length + activityLevels.length
          }
        }
      });

    } catch (error) {
      ctx.throw(500, `Failed to fetch parent activities: ${error.message}`);
    }
  },

  async parentHome(ctx) {
    try {
      const { query } = ctx;
      const now = new Date();

      // Extract common filters
      const commonFilters = {};

      // Handle student filter
      if (query.filters?.student?.id) {
        commonFilters.student = { id: query.filters.student.id };
      }

      // Handle nursery filter
      if (query.filters?.nursery?.id) {
        commonFilters.nursery = { id: query.filters.nursery.id };
      }

      // 1. Current Teacher Activity (happening now)
      const currentDate = now.toISOString().split('T')[0]; // YYYY-MM-DD
      const dayName = now.toLocaleDateString('en-US', { weekday: 'long' });

      const teacherActivityQuery = {
        filters: {
          ...commonFilters,
          $or: [
            { day: dayName },
            { date: currentDate }
          ]
        },
        populate: {
          teacher: { populate: { logo: true } },
          activity_notes: { populate: { media: true } },
          activity: { populate: { image: true } },
          class: { populate: { logo: true } }
        },
        sort: 'createdAt:desc'
      };

      const allTeacherActivities = await strapi.entityService.findMany(
        'api::teacher-activity.teacher-activity',
        teacherActivityQuery
      );

      // Filter activities based on date and is_weekly_activity
      const todayDateActivity = now.toISOString().split('T')[0]; // YYYY-MM-DD format

      const filteredActivities = allTeacherActivities.filter(activity => {
        // If activity has a date and is not weekly
        if (activity.date && activity.is_weekly_activity === false) {
          // Only include if date matches today
          return activity.date === todayDateActivity;
        }
        // Include all weekly activities or those without dates
        return true;
      });

      // Filter current activities (happening now)
      const currentTime = now.getHours() * 60 + now.getMinutes(); // minutes since midnight

      const currentActivities = filteredActivities.filter(activity => {
        if (!activity.from || !activity.to) return false;

        const [fromHour, fromMin] = activity.from.split(':').map(Number);
        const [toHour, toMin] = activity.to.split(':').map(Number);

        const fromTime = fromHour * 60 + fromMin;
        const toTime = toHour * 60 + toMin;

        return currentTime >= fromTime && currentTime <= toTime;
      });

      // 2. Today's Activities (all activities for today)
      const todayActivities = filteredActivities;

      // 3. Messages (limit 2, exclude parent type)
      const messageQuery = {
        filters: {
          ...commonFilters,
          type: { $ne: 'parent' }
        },
        populate: {
          admin: true,
          teacher: true,
          student: { populate: { image: true } }
        },
        sort: 'createdAt:desc',
        pagination: { pageSize: 2 }
      };

      const messages = await strapi.entityService.findMany(
        'api::message-center.message-center',
        messageQuery
      );

      // 4. Exams (current month)
      const monthYear = now.toISOString().slice(0, 7); // YYYY-MM

      const examQuery = {
        filters: {
          ...commonFilters,
          date: { $contains: monthYear }
        },
        populate: {
          teacher: true,
          class: true,
          students_result: {
            populate: {
              student: { populate: { image: true } }
            }
          }
        },
        sort: 'createdAt:asc'
      };

      const exams = await strapi.entityService.findMany('api::exam.exam', examQuery);

      // 5. Today's Attendance
      const todayDate = currentDate;

      const attendanceQuery = {
        filters: {
          ...commonFilters,
          attendance_date: todayDate
        },
        populate: {
          teacher: true,
          class: true,
          student: true
        },
        sort: 'createdAt:desc'
      };

      const attendance = await strapi.entityService.findMany(
        'api::attendance.attendance',
        attendanceQuery
      );

      // 6. Announcements (limit 2, target all or parents)
      const announcementQuery = {
        filters: {
          $or: [
            { target: 'all' },
            { target: 'parents' }
          ],
          $and: [
            {
              $or: [
                { nursery: { id: query.filters?.nursery?.id } },
                { nursery: { admin: { email: '<EMAIL>' } } }
              ]
            }
          ]
        },
        sort: 'createdAt:desc',
        pagination: { pageSize: 2 }
      };

      const announcements = await strapi.entityService.findMany(
        'api::announcement.announcement',
        announcementQuery
      );

      // 7. Teacher Supplies (mark_as_sent = false)
      const supplyQuery = {
        filters: {
          ...commonFilters,
          mark_as_sent: false
        },
        populate: {
          supplies: true,
          student: true,
          teacher: true
        },
        sort: 'createdAt:desc'
      };

      const supplies = await strapi.entityService.findMany(
        'api::teacher-supply.teacher-supply',
        supplyQuery
      );

      // Return combined data
      ctx.send({
        data: {
          currentActivities,
          todayActivities,
          messages,
          exams,
          attendance,
          announcements,
          supplies
        },
        meta: {
          timestamp: now.toISOString(),
          counts: {
            currentActivities: currentActivities.length,
            todayActivities: todayActivities.length,
            messages: messages.length,
            exams: exams.length,
            attendance: attendance.length,
            announcements: announcements.length,
            supplies: supplies.length
          }
        }
      });

    } catch (error) {
      ctx.throw(500, `Failed to fetch parent home data: ${error.message}`);
    }
  },
}));
