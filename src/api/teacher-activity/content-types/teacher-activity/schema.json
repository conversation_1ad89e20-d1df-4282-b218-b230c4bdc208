{"kind": "collectionType", "collectionName": "teacher_activities", "info": {"singularName": "teacher-activity", "pluralName": "teacher-activities", "displayName": "Teacher Activity", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"day": {"type": "string"}, "from": {"type": "string"}, "to": {"type": "string"}, "activity": {"type": "relation", "relation": "oneToOne", "target": "api::activity.activity"}, "class": {"type": "relation", "relation": "oneToOne", "target": "api::class.class"}, "teacher": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "nursery": {"type": "relation", "relation": "oneToOne", "target": "api::nursery.nursery"}, "date": {"type": "string"}, "is_weekly_activity": {"type": "boolean", "default": true}, "activity_notes": {"displayName": "Activity Note", "type": "component", "repeatable": true, "component": "activity-notes.activity-note"}}}