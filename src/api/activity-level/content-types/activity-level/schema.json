{"kind": "collectionType", "collectionName": "activity_levels", "info": {"singularName": "activity-level", "pluralName": "activity-levels", "displayName": "Activity Level"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"level": {"type": "enumeration", "enum": ["low", "medium", "high"]}, "note": {"type": "text"}, "student": {"type": "relation", "relation": "oneToOne", "target": "api::student.student"}, "nursery": {"type": "relation", "relation": "oneToOne", "target": "api::nursery.nursery"}, "teacher": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}}}