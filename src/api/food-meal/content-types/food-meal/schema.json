{"kind": "collectionType", "collectionName": "food_meals", "info": {"singularName": "food-meal", "pluralName": "food-meals", "displayName": "Food Meals", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"meal": {"type": "string"}, "type": {"type": "enumeration", "enum": ["Breakfast", "Snack", "Lunch"]}, "day": {"type": "enumeration", "enum": ["Friday", "Saturday", "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday"]}, "nursery": {"type": "relation", "relation": "oneToOne", "target": "api::nursery.nursery"}}}