{"kind": "collectionType", "collectionName": "exams", "info": {"singularName": "exam", "pluralName": "exams", "displayName": "Exam", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"question": {"type": "text"}, "students_result": {"displayName": "Student Exam", "type": "component", "repeatable": true, "component": "student-exam.student-exam"}, "class": {"type": "relation", "relation": "oneToOne", "target": "api::class.class"}, "nursery": {"type": "relation", "relation": "oneToOne", "target": "api::nursery.nursery"}, "teacher": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "date": {"type": "string"}}}