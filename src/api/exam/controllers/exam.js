//'use strict';
//
///**
// * exam controller
// */
//
//const { createCoreController } = require('@strapi/strapi').factories;
//
//module.exports = createCoreController('api::exam.exam');
'use strict';

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::exam.exam', ({ strapi }) => ({
  async updateMany(ctx) {
    const { exams } = ctx.request.body;

    if (!Array.isArray(exams)) {
      return ctx.badRequest('Exams data should be an array');
    }

    const updatePromises = exams.map((exam) => {
      const { id, ...data } = exam;
      return strapi.entityService.update('api::exam.exam', id, { data });
    });

    try {
      const updatedExams = await Promise.all(updatePromises);
      ctx.send({ updatedExams });
    } catch (error) {
      ctx.throw(500, 'Failed to update exams');
    }
  },
}));
