{"kind": "collectionType", "collectionName": "foods", "info": {"singularName": "food", "pluralName": "foods", "displayName": "Food", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"meal_type": {"type": "string"}, "teacher": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "student": {"type": "relation", "relation": "oneToOne", "target": "api::student.student"}, "nursery": {"type": "relation", "relation": "oneToOne", "target": "api::nursery.nursery"}, "meal_amount": {"type": "string"}}}