
const path = require('path');

module.exports = ({ env }) => ({
  'users-permissions': {
    config: {
      jwtSecret: env('JWT_SECRET'),
    },
  },
  upload: {
        config: {
          provider: "strapi-provider-firebase-storage",
          providerOptions: {
            serviceAccount: require(path.resolve(process.cwd(), 'connectify-service-account.json')),
            bucket: env(
              "STORAGE_BUCKET_URL"
            ),
            sortInStorage: true,
            debug: false,
          },
        },
      },
});
