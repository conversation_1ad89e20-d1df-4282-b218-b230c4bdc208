{"name": "strapi", "private": true, "version": "0.1.0", "description": "A Strapi application", "scripts": {"develop": "strapi develop", "start": "strapi start", "build": "strapi build", "strapi": "strapi"}, "dependencies": {"@strapi/plugin-i18n": "4.25.16", "@strapi/plugin-users-permissions": "4.25.16", "@strapi/provider-upload-cloudinary": "4.25.16", "@strapi/strapi": "4.25.16", "pg": "8.11.3", "pg-connection-string": "^2.8.5", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^5.3.4", "strapi-plugin-populate-deep": "^3.0.1", "strapi-provider-firebase-storage": "^1.0.4", "styled-components": "^5.3.11"}, "author": {"name": "A Strapi developer"}, "strapi": {"uuid": "35d7a084-3088-4d0b-869e-f503693ee8e5"}, "engines": {"node": "20"}, "license": "MIT"}